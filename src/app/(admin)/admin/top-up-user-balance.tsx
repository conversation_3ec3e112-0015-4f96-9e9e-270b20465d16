'use client';

import { DollarSign, Minus, RotateCcw, Search, User } from 'lucide-react';
import { useState } from 'react';

import {
  decreaseUserBalance,
  resetUserBalance,
  topUpUserBalance,
} from '@/api/admin-api';
import { searchUsersByTelegramHandle } from '@/api/user-api';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import type { UserEntity } from '@/constants/core.constants';
import { useToast } from '@/hooks/use-toast';

export function TopUpUserBalance() {
  const [telegramHandle, setTelegramHandle] = useState('');
  const [amount, setAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [foundUser, setFoundUser] = useState<UserEntity | null>(null);
  const { toast } = useToast();

  const handleSearchUser = async () => {
    if (!telegramHandle.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a telegram handle',
        variant: 'destructive',
      });
      return;
    }

    setSearchLoading(true);
    try {
      const users = await searchUsersByTelegramHandle(telegramHandle.trim());

      if (users.length === 0) {
        setFoundUser(null);
        toast({
          title: 'User not found',
          description: `No user found with telegram handle: ${telegramHandle}`,
          variant: 'destructive',
        });
      } else {
        setFoundUser(users[0]);
        toast({
          title: 'User found',
          description: `Found user: ${users[0].displayName || 'Unknown'}`,
        });
      }
    } catch (error) {
      console.error('Error searching user:', error);
      toast({
        title: 'Search failed',
        description: 'Failed to search for user. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSearchLoading(false);
    }
  };

  const handleTopUp = async () => {
    if (!foundUser) {
      toast({
        title: 'Error',
        description: 'Please search and select a user first',
        variant: 'destructive',
      });
      return;
    }

    if (!amount.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter an amount',
        variant: 'destructive',
      });
      return;
    }

    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount) || numericAmount <= 0) {
      toast({
        title: 'Error',
        description: 'Please enter a valid positive amount',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      await topUpUserBalance(foundUser.id, numericAmount);

      toast({
        title: 'Success',
        description: `Successfully topped up ${numericAmount} TON to ${foundUser.displayName || foundUser.telegram_handle}'s balance`,
      });

      // Reset form
      setTelegramHandle('');
      setAmount('');
      setFoundUser(null);
    } catch (error) {
      console.error('Error topping up user balance:', error);
      toast({
        title: 'Top-up failed',
        description: 'Failed to top up user balance. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDecrease = async () => {
    if (!foundUser) {
      toast({
        title: 'Error',
        description: 'Please search and select a user first',
        variant: 'destructive',
      });
      return;
    }

    if (!amount.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter an amount',
        variant: 'destructive',
      });
      return;
    }

    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount) || numericAmount <= 0) {
      toast({
        title: 'Error',
        description: 'Please enter a valid positive amount',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      await decreaseUserBalance(foundUser.id, numericAmount);

      toast({
        title: 'Success',
        description: `Successfully decreased ${foundUser.displayName || foundUser.telegram_handle}'s balance by ${numericAmount} TON`,
      });

      // Reset form
      setTelegramHandle('');
      setAmount('');
      setFoundUser(null);
    } catch (error) {
      console.error('Error decreasing user balance:', error);
      toast({
        title: 'Decrease failed',
        description: 'Failed to decrease user balance. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleReset = async () => {
    if (!foundUser) {
      toast({
        title: 'Error',
        description: 'Please search and select a user first',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      await resetUserBalance(foundUser.id);

      toast({
        title: 'Success',
        description: `Successfully reset ${foundUser.displayName || foundUser.telegram_handle}'s balance (sum=0, locked=0)`,
      });

      // Reset form
      setTelegramHandle('');
      setAmount('');
      setFoundUser(null);
    } catch (error) {
      console.error('Error resetting user balance:', error);
      toast({
        title: 'Reset failed',
        description: 'Failed to reset user balance. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAmountChange = (value: string) => {
    const regex = /^\d*\.?\d*$/;
    if (regex.test(value) || value === '') {
      setAmount(value);
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <DollarSign className="h-4 w-4" />
          Manage User Balance
        </CardTitle>
        <CardDescription className="text-sm">
          Search for a user by telegram handle and manage their balance
          (increase, decrease, or reset)
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="telegramHandle">Telegram Handle</Label>
            <div className="flex gap-2">
              <Input
                id="telegramHandle"
                type="text"
                placeholder="Enter telegram handle (without @)"
                value={telegramHandle}
                onChange={(e) => setTelegramHandle(e.target.value)}
                disabled={loading || searchLoading}
                className="flex-1 border-2 border-gray-300 focus:border-blue-500"
              />
              <Button
                onClick={handleSearchUser}
                disabled={loading || searchLoading || !telegramHandle.trim()}
                variant="outline"
                size="default"
              >
                <Search className="h-4 w-4 mr-2" />
                {searchLoading ? 'Searching...' : 'Search'}
              </Button>
            </div>
          </div>

          {foundUser && (
            <div className="p-3 bg-muted rounded-lg border">
              <div className="flex items-center gap-2 text-sm">
                <User className="h-4 w-4" />
                <span className="font-medium">
                  {foundUser.displayName || 'Unknown Name'}
                </span>
                <span className="text-muted-foreground">
                  (@{foundUser.telegram_handle})
                </span>
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                Balance: {foundUser.balance?.sum?.toFixed(2) || '0.00'} TON
                (sum) | {foundUser.balance?.locked?.toFixed(2) || '0.00'} TON
                (locked)
              </div>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="amount">Amount (TON)</Label>
            <Input
              id="amount"
              type="text"
              placeholder="Enter amount to add (e.g., 10.5)"
              value={amount}
              onChange={(e) => handleAmountChange(e.target.value)}
              disabled={loading || !foundUser}
              className="border-2 border-gray-300 focus:border-blue-500"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
            <Button
              onClick={handleTopUp}
              disabled={loading || !foundUser || !amount.trim()}
              className="w-full"
            >
              <DollarSign className="h-4 w-4 mr-2" />
              {loading ? 'Processing...' : `Increase ${amount || '0'} TON`}
            </Button>

            <Button
              onClick={handleDecrease}
              disabled={loading || !foundUser || !amount.trim()}
              variant="outline"
              className="w-full"
            >
              <Minus className="h-4 w-4 mr-2" />
              {loading ? 'Processing...' : `Decrease ${amount || '0'} TON`}
            </Button>

            <Button
              onClick={handleReset}
              disabled={loading || !foundUser}
              variant="destructive"
              className="w-full"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              {loading ? 'Processing...' : 'Reset Balance'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
