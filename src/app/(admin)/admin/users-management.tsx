'use client';

import { useCallback, useEffect } from 'react';

import { getUsersWithPagination } from '@/api/user-api';
import { Pagination } from '@/components/ui/pagination';
import type { UserEntity } from '@/constants/core.constants';
import { usePagePagination } from '@/hooks/use-page-pagination';

import { UsersHeader } from './users-management/users-header';
import { UsersTable } from './users-management/users-table';

export function UsersManagement() {
  const fetchUsersPage = useCallback(async (page: number, pageSize: number) => {
    return await getUsersWithPagination(page, pageSize);
  }, []);

  const {
    items: users,
    loading,
    currentPage,
    totalPages,
    totalItems,
    goToPage,
    loadPage,
    refresh,
  } = usePagePagination<UserEntity>(fetchUsersPage, {
    pageSize: 25,
  });

  useEffect(() => {
    loadPage(1);
  }, [loadPage]);

  const handlePageChange = (page: number) => {
    goToPage(page);
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <UsersHeader onRefresh={refresh} totalUsers={totalItems} />

        <UsersTable users={users} loading={loading} />

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          className="mt-4"
        />
      </div>
    </div>
  );
}
