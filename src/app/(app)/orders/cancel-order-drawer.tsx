'use client';

import { <PERSON><PERSON><PERSON>riangle, Loader2 } from 'lucide-react';
import { useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';
import { Drawer } from 'vaul';

import { cancelOrder } from '@/api/order-api';
import { ConfirmWrapper } from '@/components/shared/confirm-wrapper';
import { Button } from '@/components/ui/button';
import type { OrderEntity } from '@/constants/core.constants';
import { OrderStatus } from '@/constants/core.constants';
import { useRootContext } from '@/root-context';
import {
  calculateCollateralLoss,
  hasResellerEarnings,
  isOriginalSeller,
} from '@/services/order-service';
import { formatError } from '@/utils/error-handler';

import { cancelOrderDrawerMessages } from './intl/cancel-order-drawer.messages';

interface CancelOrderDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  onOrderCancelled: () => void;
}

export function CancelOrderDrawer({
  open,
  onOpenChange,
  order,
  onOrderCancelled,
}: CancelOrderDrawerProps) {
  const { formatMessage: t } = useIntl();
  const { currentUser, appConfig, refetchUser } = useRootContext();
  const [cancelling, setCancelling] = useState(false);

  const collateralLoss = calculateCollateralLoss(order, currentUser);
  const isOrderOriginalSeller = isOriginalSeller(currentUser, order);
  const orderHasResellerEarnings = hasResellerEarnings(order);

  const handleCancelOrder = async () => {
    if (!order || !currentUser) return;

    setCancelling(true);
    try {
      const result = await cancelOrder(order.id as string, currentUser.id);

      const data = result as { success: boolean; message: string };

      if (data.success) {
        toast.success(t(cancelOrderDrawerMessages.orderCancelledSuccessfully));
        await refetchUser();
        onOrderCancelled();
        onOpenChange(false);
      } else {
        toast.error(
          data.message ||
            t(cancelOrderDrawerMessages.failedToCancelOrder, { message: '' }),
        );
      }
    } catch (error: unknown) {
      console.error('Error cancelling order:', error);
      const errorMessage = formatError(error, t);
      toast.error(errorMessage);
    } finally {
      setCancelling(false);
    }
  };

  if (!order) return null;

  return (
    <Drawer.Root open={open} onOpenChange={onOpenChange}>
      <Drawer.Portal>
        <Drawer.Title />
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-100" />
        <Drawer.Content className="bg-[#232e3c] flex flex-col rounded-t-[10px] h-auto mt-24 fixed bottom-0 left-0 right-0 z-101">
          <div className="p-6 bg-[#232e3c] rounded-t-[10px]">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#3a4a5c] mb-6" />

            <div className="space-y-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <AlertTriangle className="w-8 h-8 text-red-400" />
                </div>
                <h2 className="text-xl font-bold text-[#f5f5f5]">
                  {t(cancelOrderDrawerMessages.cancelOrder)} #
                  {order.number || order.id?.slice(-6)}
                </h2>
              </div>

              <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
                  <div className="space-y-2">
                    <h3 className="text-red-400 font-medium">
                      {order.status === OrderStatus.PAID
                        ? 'Collateral Lost'
                        : 'Warning: Penalty Fee Will Be Applied'}
                    </h3>
                    <p className="text-[#708499] text-sm">
                      {order.status === OrderStatus.PAID
                        ? `You will lose ${collateralLoss.toFixed(4)} TON in collateral. This action is permanent and cannot be undone.`
                        : `If you cancel this order, a penalty fee of ${
                            appConfig?.fixed_cancel_order_fee?.toFixed(4) ||
                            '0.1000'
                          } TON will be taken from your balance. This action is permanent and cannot be undone.`}
                    </p>
                    {order.status === OrderStatus.PAID &&
                      isOrderOriginalSeller &&
                      orderHasResellerEarnings && (
                        <p className="text-red-400 text-sm font-medium">
                          Additionally, you will lose{' '}
                          {order.reseller_earnings_for_seller?.toFixed(4)} TON
                          in reseller earnings.
                        </p>
                      )}
                  </div>
                </div>
              </div>

              <div className="text-center">
                <p className="text-[#708499] text-sm">
                  {t(cancelOrderDrawerMessages.confirmCancellation)}
                </p>
              </div>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={cancelling}
                  className="flex-1 border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#3a4a5c]"
                >
                  {t(cancelOrderDrawerMessages.keepOrder)}
                </Button>
                <ConfirmWrapper>
                  <Button
                    variant="destructive"
                    onClick={handleCancelOrder}
                    disabled={cancelling}
                    className="flex-1"
                  >
                    {cancelling ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        {t(cancelOrderDrawerMessages.cancelling)}
                      </>
                    ) : (
                      t(cancelOrderDrawerMessages.cancel)
                    )}
                  </Button>
                </ConfirmWrapper>
              </div>
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
