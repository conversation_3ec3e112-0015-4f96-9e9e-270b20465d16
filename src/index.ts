import bot from "./bot";
import { HealthcheckService } from "./services/healthcheck";
import { expressHttpServer } from "./services/express-server";
import { loadEnvironment } from "./config/env-loader";
import { log } from "./utils/logger";

loadEnvironment();

const PORT = process.env.PORT ?? 8080;
const NODE_ENV = process.env.NODE_ENV ?? "development";
const WEBHOOK_URL = process.env.WEBHOOK_URL;
const WEB_APP_URL = process.env.WEB_APP_URL as string;

log.info("Startup Configuration", {
  operation: "bot_startup",
  NODE_ENV,
  PORT,
  WEBHOOK_URL: WEBHOOK_URL ? WEBHOOK_URL.substring(0, 50) + "..." : "Not set",
  processId: process.pid,
});

// @ts-expect-error note
async function setupWebhook() {
  try {
    const webhookUrl = `${WEBHOOK_URL}/webhook`;

    await bot.telegram.deleteWebhook({ drop_pending_updates: true });
    log.webhookLog("Cleared existing webhook and dropped pending updates", {
      operation: "webhook_setup",
      webhookUrl,
    });

    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Set the new webhook with configuration
    await bot.telegram.setWebhook(webhookUrl, {
      max_connections: 40,
      allowed_updates: [
        "message",
        "callback_query",
        "inline_query",
        "chosen_inline_result",
        "edited_message",
        "channel_post",
        "edited_channel_post",
      ],
    });
    log.webhookLog(`Webhook set to: ${webhookUrl}`, {
      operation: "webhook_setup",
      webhookUrl,
      status: "success",
    });

    const webhookInfo = await bot.telegram.getWebhookInfo();
    log.webhookLog("Webhook info retrieved", {
      operation: "webhook_setup",
      url: webhookInfo?.url as string,
      has_custom_certificate: webhookInfo.has_custom_certificate,
      pending_update_count: webhookInfo.pending_update_count,
      last_error_date: webhookInfo.last_error_date,
      last_error_message: webhookInfo.last_error_message,
      max_connections: webhookInfo.max_connections,
      allowed_updates: webhookInfo.allowed_updates,
    });

    if (webhookInfo.url !== webhookUrl) {
      throw new Error(
        `Webhook URL mismatch. Expected: ${webhookUrl}, Got: ${webhookInfo.url}`
      );
    }

    if (
      webhookInfo.pending_update_count &&
      webhookInfo.pending_update_count > 0
    ) {
      log.warn(
        `Warning: ${webhookInfo.pending_update_count} pending updates found`,
        {
          operation: "webhook_setup",
          pending_update_count: webhookInfo.pending_update_count,
        }
      );
    }
  } catch (error) {
    log.error("Failed to set webhook", error, {
      operation: "webhook_setup",
      webhookUrl: `${WEBHOOK_URL}/webhook`,
    });
    throw error;
  }
}

async function startBot() {
  try {
    log.botLog("Starting Marketplace Bot", {
      operation: "bot_startup",
    });

    HealthcheckService.setBotStartTime();

    await expressHttpServer.start();

    bot
      .launch({
        webhook: {
          domain: WEBHOOK_URL as string,
          path: "/webhook",
        },
      })
      .then(() => {
        expressHttpServer.setReady(true);
      })
      .catch((error: any) => {
        log.error("Failed to launch bot", error, {
          operation: "bot_startup",
          mode: "polling",
        });
        if (
          error.message?.includes("409") ||
          error.message?.includes("Conflict")
        ) {
          log.warn(
            "Another bot instance might be running. Please stop it first.",
            {
              operation: "bot_startup",
              mode: "polling",
              error_type: "conflict",
            }
          );
        }
      });

    try {
      await bot.telegram.setChatMenuButton({
        menuButton: {
          type: "web_app",
          text: "PREM",
          web_app: {
            url: WEB_APP_URL,
          },
        },
      });

      log.botLog("Menu button configured successfully", {
        operation: "bot_configuration",
        component: "menu_button",
        status: "success",
        menuButtonText: "PREM",
        webAppUrl: WEB_APP_URL.substring(0, 50) + "...",
      });

      const menuButton = await bot.telegram.getChatMenuButton();
      log.botLog("Menu button verification", {
        operation: "bot_configuration",
        component: "menu_button_verification",
        menuButtonType: menuButton.type,
        menuButtonText: (menuButton as any).text,
        webAppUrl: (menuButton as any).web_app?.url?.substring(0, 50) + "...",
      });
    } catch (error) {
      log.error("Failed to set menu button", error, {
        operation: "bot_configuration",
        component: "menu_button",
        webAppUrl: WEB_APP_URL,
      });
    }

    try {
      await bot.telegram.setMyCommands([
        { command: "start", description: "Start the bot and show main menu" },
        { command: "help", description: "Show help information" },
        { command: "health", description: "Check bot health status" },
      ]);
      log.botLog("Commands configured", {
        operation: "bot_configuration",
        component: "commands",
        status: "success",
      });
    } catch (error) {
      log.warn("Failed to set commands", {
        operation: "bot_configuration",
        component: "commands",
        error,
      });
    }

    try {
      const description =
        "Welcome to PREM🎁 – the first liquid pre-market for Telegram unupgraded gifts!\nWith PREM, you can:\n\nAs buyer:\n\n🔓 Buy any unupgraded TG gift.\n💸 Resell for instant profit\n\nAs seller:\n\n🎁 Sell unupgraded TG gift with just 50% collateral.\n💰 Earn fees from resales\n\nEnjoy fast, safe, and easy gift trading!";
      const shortDescription = "🎁 PREM - Telegram Gifts Marketplace";

      await bot.telegram.setMyDescription(description);
      await bot.telegram.setMyShortDescription(shortDescription);

      log.botLog("Bot description configured", {
        operation: "bot_configuration",
        component: "description",
        status: "success",
        description: description.substring(0, 100) + "...",
        shortDescription,
      });
    } catch (error) {
      log.error("Failed to set bot description", error, {
        operation: "bot_configuration",
        component: "description",
      });
    }

    log.botLog("Bot setup completed successfully", {
      operation: "bot_startup",
      status: "completed",
      features: [
        "My Buy Orders",
        "My Sell Orders",
        "Get Referral Link",
        "Contact Support",
        "Open Marketplace (Web App)",
      ],
    });
  } catch (error) {
    log.error("Failed to start bot", error, {
      operation: "bot_startup",
    });
    process.exit(1);
  }
}

async function gracefulShutdown(signal: string) {
  log.info(`Received ${signal}, shutting down gracefully`, {
    operation: "bot_shutdown",
    signal,
  });

  const forceExitTimeout = setTimeout(() => {
    log.error("Graceful shutdown timeout, forcing exit", undefined, {
      operation: "bot_shutdown",
      signal,
    });
    process.exit(1);
  }, 8000); // 8 seconds, leaving 2 seconds buffer before Cloud Run SIGKILL

  try {
    log.botLog("Stopping bot", {
      operation: "bot_shutdown",
      step: "bot",
    });
    bot.stop(signal);

    log.info("Stopping HTTP server", {
      operation: "bot_shutdown",
      step: "http_server",
    });
    await expressHttpServer.stop();

    log.info("Skipping webhook cleanup for faster shutdown", {
      operation: "bot_shutdown",
      step: "webhook_cleanup_skip",
    });

    clearTimeout(forceExitTimeout);
    log.info("Graceful shutdown completed", {
      operation: "bot_shutdown",
      status: "completed",
    });
    process.exit(0);
  } catch (error) {
    clearTimeout(forceExitTimeout);
    log.error("Error during shutdown", error, {
      operation: "bot_shutdown",
      status: "error",
    });
    process.exit(1);
  }
}

process.once("SIGINT", () => gracefulShutdown("SIGINT"));
process.once("SIGTERM", () => gracefulShutdown("SIGTERM"));

startBot();
